import { useEffect, useState } from "react";

function Products() {
  const [products, setProducts] = useState([]);

  useEffect(() => {
    fetch("http://192.168.100.118:8000/api/products/")
      .then((response) => {
        console.log("sth");
        return response.json();
      })
      .then((data) => {
        console.log("got data");
        setProducts(data);
      })
      .catch((error) => console.error(error));
  }, []);

  return (
    <div>
        <h1>Products List</h1>
        {products.length ===0 ? <p>No products</p>: 
        (
            <ul>
                {products.map((product) => (
                    <li key={product.id}>{product.name}</li>
                ))}
            </ul>
        )}
    </div>
  );
}

export default Products;
