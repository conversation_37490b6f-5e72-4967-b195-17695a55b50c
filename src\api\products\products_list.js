import { useEffect, useState } from "react";

function Products() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true); // loading state
  const [error, setError] = useState(null);     // error state

  useEffect(() => {
    fetch("http://192.168.100.118:8000/api/products/")
      .then((response) => response.json())
      .then((data) => {
        console.log("Data received:", data);
        if (Array.isArray(data)) {
          setProducts(data);                 // only set if it's an array
        } else {
          console.warn("Backend returned non-array:", data);
          setProducts([]);                   // fallback
        }
      })
      .catch((err) => {
        console.error("Fetch error:", err);
        setError(err);                       // store error
      })
      .finally(() => setLoading(false));      // stop loading
  }, []);

  if (loading) return <p>Loading products...</p>;
  if (error) return <p>Error loading products.</p>;

  return (
    <div>
      <h1>Products List</h1>
      {products.length === 0 ? (
        <p>No products available.</p>
      ) : (
        <ul>
          {products.map((product, index) => (
            <li key={product.id || index}>{product.name || "No name"}</li>
          ))}
        </ul>
      )}
    </div>
  );
}

export default Products;
