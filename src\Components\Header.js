import { useState } from 'react';
import { Link } from 'react-router-dom';
import './Header.css';

function Header() {
  const [isMobileOpen, setIsMobileOpen] = useState(false);

  const toggleMobile = () => {
    setIsMobileOpen(!isMobileOpen);
  };

  return (
    <>
      {/* Mobile toggle button */}
      <button
        className="sidebar__mobile-toggle"
        onClick={toggleMobile}
        aria-label="Toggle navigation menu"
      >
        ☰
      </button>

      {/* Mobile overlay */}
      <div
        className={`sidebar-overlay ${isMobileOpen ? 'sidebar-overlay--visible' : ''}`}
        onClick={toggleMobile}
      />

      {/* Sidebar */}
      <aside className={`sidebar ${isMobileOpen ? 'sidebar--mobile-open' : ''}`}>
        <div className="sidebar__container">
          {/* Brand/Logo Section */}
          <div className="sidebar__brand">
            <div className="sidebar__brand-logo">
              IM
            </div>
            <span className="sidebar__brand-text">Inventory Manager</span>
          </div>

          {/* Main Navigation */}
          <nav className="sidebar__nav">
            {/* Main Section */}
            <div className="sidebar__nav-section">
              <h3 className="sidebar__nav-title">Main</h3>
              <div className="sidebar__links">
                <Link to="/" className="sidebar__link sidebar__link--active">
                  <span className="sidebar__link-icon">🏠</span>
                  <span className="sidebar__link-text">Dashboard</span>
                </Link>
                <Link to="/products" className="sidebar__link">
                  <span className="sidebar__link-icon">📦</span>
                  <span className="sidebar__link-text">Products</span>
                </Link>
                <Link to="/categories" className="sidebar__link">
                  <span className="sidebar__link-icon">🏷️</span>
                  <span className="sidebar__link-text">Categories</span>
                </Link>
                <Link to="/inventory" className="sidebar__link">
                  <span className="sidebar__link-icon">📊</span>
                  <span className="sidebar__link-text">Inventory</span>
                </Link>
              </div>
            </div>

            {/* Orders Section */}
            <div className="sidebar__nav-section">
              <h3 className="sidebar__nav-title">Orders</h3>
              <div className="sidebar__links">
                <Link to="/orders" className="sidebar__link">
                  <span className="sidebar__link-icon">🛒</span>
                  <span className="sidebar__link-text">All Orders</span>
                </Link>
                <Link to="/orders/pending" className="sidebar__link">
                  <span className="sidebar__link-icon">⏳</span>
                  <span className="sidebar__link-text">Pending</span>
                </Link>
                <Link to="/orders/completed" className="sidebar__link">
                  <span className="sidebar__link-icon">✅</span>
                  <span className="sidebar__link-text">Completed</span>
                </Link>
              </div>
            </div>

            {/* Reports Section */}
            <div className="sidebar__nav-section">
              <h3 className="sidebar__nav-title">Analytics</h3>
              <div className="sidebar__links">
                <Link to="/reports" className="sidebar__link">
                  <span className="sidebar__link-icon">📈</span>
                  <span className="sidebar__link-text">Reports</span>
                </Link>
                <Link to="/analytics" className="sidebar__link">
                  <span className="sidebar__link-icon">📊</span>
                  <span className="sidebar__link-text">Analytics</span>
                </Link>
              </div>
            </div>
          </nav>

          {/* Footer/User Section */}
          <div className="sidebar__footer">
            <div className="sidebar__links">
              <Link to="/settings" className="sidebar__link">
                <span className="sidebar__link-icon">⚙️</span>
                <span className="sidebar__link-text">Settings</span>
              </Link>
            </div>

            <div className="sidebar__user">
              <div className="sidebar__user-avatar">
                JD
              </div>
              <div className="sidebar__user-info">
                <div className="sidebar__user-name">John Doe</div>
                <div className="sidebar__user-role">Administrator</div>
              </div>
            </div>
          </div>
        </div>
      </aside>
    </>
  );
}

export default Header
