/* Header.css - Modern white and blue header styling */

/* CSS Custom Properties for consistent theming */
:root {
  --header-primary-bg: #ffffff;
  --header-secondary-bg: #f8fafc;
  --header-primary-blue: #2563eb;
  --header-secondary-blue: #3b82f6;
  --header-light-blue: #dbeafe;
  --header-text-primary: #1e293b;
  --header-text-secondary: #64748b;
  --header-border: #e2e8f0;
  --header-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --header-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --header-transition: all 0.2s ease-in-out;
  --header-border-radius: 0.375rem;
  --header-max-width: 1200px;
}

/* Main header container */
.header {
  background-color: var(--header-primary-bg);
  border-bottom: 1px solid var(--header-border);
  box-shadow: var(--header-shadow);
  position: sticky;
  top: 0;
  z-index: 1000;
  width: 100%;
  transition: var(--header-transition);
}

/* Header content wrapper */
.header__container {
  max-width: var(--header-max-width);
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 4rem;
  position: relative;
}

/* Navigation container */
.header__nav {
  display: flex;
  align-items: center;
  gap: 2rem;
  flex: 1;
}

/* Logo/Brand area (reserved for future use) */
.header__brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--header-primary-blue);
  text-decoration: none;
  transition: var(--header-transition);
}

.header__brand:hover {
  color: var(--header-secondary-blue);
}

/* Main navigation links */
.header__links {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  list-style: none;
  margin: 0;
  padding: 0;
}

.header__link {
  color: var(--header-text-primary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
  border-radius: var(--header-border-radius);
  transition: var(--header-transition);
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.header__link:hover {
  color: var(--header-primary-blue);
  background-color: var(--header-light-blue);
  transform: translateY(-1px);
}

.header__link:focus {
  outline: 2px solid var(--header-primary-blue);
  outline-offset: 2px;
}

.header__link--active {
  color: var(--header-primary-blue);
  background-color: var(--header-light-blue);
  font-weight: 600;
}

/* Actions area (reserved for future buttons, user menu, etc.) */
.header__actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Mobile menu toggle (for future mobile navigation) */
.header__mobile-toggle {
  display: none;
  background: none;
  border: none;
  color: var(--header-text-primary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--header-border-radius);
  transition: var(--header-transition);
}

.header__mobile-toggle:hover {
  background-color: var(--header-secondary-bg);
  color: var(--header-primary-blue);
}

/* Responsive Design */

/* Tablet styles */
@media (max-width: 768px) {
  .header__container {
    padding: 0 0.75rem;
    min-height: 3.5rem;
  }
  
  .header__nav {
    gap: 1rem;
  }
  
  .header__links {
    gap: 1rem;
  }
  
  .header__link {
    padding: 0.375rem 0.75rem;
    font-size: 0.8125rem;
  }
  
  .header__brand {
    font-size: 1.125rem;
  }
}

/* Mobile styles */
@media (max-width: 640px) {
  .header__container {
    padding: 0 0.5rem;
    min-height: 3rem;
  }
  
  .header__mobile-toggle {
    display: block;
  }
  
  .header__nav {
    gap: 0.5rem;
  }
  
  .header__links {
    gap: 0.5rem;
  }
  
  .header__link {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
  
  .header__brand {
    font-size: 1rem;
  }
  
  /* Hide some navigation items on very small screens if needed */
  .header__link--hide-mobile {
    display: none;
  }
}

/* Extra small mobile styles */
@media (max-width: 480px) {
  .header__container {
    flex-wrap: wrap;
    min-height: auto;
    padding: 0.5rem;
  }
  
  .header__nav {
    width: 100%;
    justify-content: center;
    margin-top: 0.5rem;
  }
  
  .header__links {
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.25rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .header,
  .header__link,
  .header__brand,
  .header__mobile-toggle {
    transition: none;
  }
  
  .header__link:hover {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .header {
    border-bottom-width: 2px;
  }
  
  .header__link {
    border: 1px solid transparent;
  }
  
  .header__link:hover,
  .header__link--active {
    border-color: var(--header-primary-blue);
  }
}

/* Dark mode support (for future implementation) */
@media (prefers-color-scheme: dark) {
  :root {
    --header-primary-bg: #0f172a;
    --header-secondary-bg: #1e293b;
    --header-text-primary: #f1f5f9;
    --header-text-secondary: #94a3b8;
    --header-border: #334155;
    --header-light-blue: #1e3a8a;
  }
}

/* Print styles */
@media print {
  .header {
    box-shadow: none;
    border-bottom: 1px solid #000;
    position: static;
  }
  
  .header__mobile-toggle {
    display: none;
  }
}
