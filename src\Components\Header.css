/* Sidebar.css - Modern white and blue sidebar navigation styling */

/* CSS Custom Properties for consistent theming */
:root {
  --sidebar-primary-bg: #ffffff;
  --sidebar-secondary-bg: #f8fafc;
  --sidebar-primary-blue: #2563eb;
  --sidebar-secondary-blue: #3b82f6;
  --sidebar-light-blue: #dbeafe;
  --sidebar-text-primary: #1e293b;
  --sidebar-text-secondary: #64748b;
  --sidebar-border: #e2e8f0;
  --sidebar-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --sidebar-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --sidebar-transition: all 0.3s ease-in-out;
  --sidebar-border-radius: 0.5rem;
  --sidebar-width: 280px;
  --sidebar-width-collapsed: 80px;
}

/* Main sidebar container */
.sidebar {
  background-color: var(--sidebar-primary-bg);
  border-right: 1px solid var(--sidebar-border);
  box-shadow: var(--sidebar-shadow);
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: var(--sidebar-width);
  z-index: 1000;
  transition: var(--sidebar-transition);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Sidebar collapsed state */
.sidebar--collapsed {
  width: var(--sidebar-width-collapsed);
}

/* Sidebar hidden state (mobile) */
.sidebar--hidden {
  transform: translateX(-100%);
}

/* Sidebar content wrapper */
.sidebar__container {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0;
}

/* Brand/Logo section */
.sidebar__brand {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid var(--sidebar-border);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background-color: var(--sidebar-secondary-bg);
}

.sidebar__brand-logo {
  width: 2rem;
  height: 2rem;
  background-color: var(--sidebar-primary-blue);
  border-radius: var(--sidebar-border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 1.125rem;
  flex-shrink: 0;
}

.sidebar__brand-text {
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--sidebar-primary-blue);
  transition: var(--sidebar-transition);
}

.sidebar--collapsed .sidebar__brand-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* Navigation container */
.sidebar__nav {
  flex: 1;
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
}

/* Navigation sections */
.sidebar__nav-section {
  margin-bottom: 1.5rem;
}

.sidebar__nav-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--sidebar-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0 1rem;
  margin-bottom: 0.5rem;
  transition: var(--sidebar-transition);
}

.sidebar--collapsed .sidebar__nav-title {
  opacity: 0;
  height: 0;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* Main navigation links */
.sidebar__links {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  list-style: none;
  margin: 0;
  padding: 0 0.5rem;
}

.sidebar__link {
  color: var(--sidebar-text-primary);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.875rem;
  padding: 0.75rem 1rem;
  border-radius: var(--sidebar-border-radius);
  transition: var(--sidebar-transition);
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.125rem;
}

.sidebar__link-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
}

.sidebar__link-text {
  transition: var(--sidebar-transition);
  white-space: nowrap;
}

.sidebar--collapsed .sidebar__link-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.sidebar__link:hover {
  color: var(--sidebar-primary-blue);
  background-color: var(--sidebar-light-blue);
  transform: translateX(4px);
}

.sidebar__link:focus {
  outline: 2px solid var(--sidebar-primary-blue);
  outline-offset: 2px;
}

.sidebar__link--active {
  color: var(--sidebar-primary-blue);
  background-color: var(--sidebar-light-blue);
  font-weight: 600;
  border-left: 3px solid var(--sidebar-primary-blue);
}

/* Bottom actions area (user menu, settings, etc.) */
.sidebar__footer {
  border-top: 1px solid var(--sidebar-border);
  padding: 1rem 0.5rem;
  margin-top: auto;
}

.sidebar__user {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  border-radius: var(--sidebar-border-radius);
  transition: var(--sidebar-transition);
  cursor: pointer;
  color: var(--sidebar-text-primary);
  text-decoration: none;
}

.sidebar__user:hover {
  background-color: var(--sidebar-secondary-bg);
}

.sidebar__user-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: var(--sidebar-primary-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  flex-shrink: 0;
}

.sidebar__user-info {
  transition: var(--sidebar-transition);
  min-width: 0;
}

.sidebar__user-name {
  font-weight: 500;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar__user-role {
  font-size: 0.75rem;
  color: var(--sidebar-text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar--collapsed .sidebar__user-info {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* Mobile toggle button */
.sidebar__mobile-toggle {
  display: none;
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 1001;
  background: var(--sidebar-primary-bg);
  border: 1px solid var(--sidebar-border);
  color: var(--sidebar-text-primary);
  font-size: 1.25rem;
  cursor: pointer;
  padding: 0.75rem;
  border-radius: var(--sidebar-border-radius);
  box-shadow: var(--sidebar-shadow);
  transition: var(--sidebar-transition);
}

.sidebar__mobile-toggle:hover {
  background-color: var(--sidebar-secondary-bg);
  color: var(--sidebar-primary-blue);
}

/* Content area adjustment */
.main-content {
  margin-left: var(--sidebar-width);
  transition: var(--sidebar-transition);
  min-height: 100vh;
  padding: 2rem;
}

.main-content--sidebar-collapsed {
  margin-left: var(--sidebar-width-collapsed);
}

.main-content--sidebar-hidden {
  margin-left: 0;
}

/* Responsive Design */

/* Tablet styles */
@media (max-width: 1024px) {
  .sidebar {
    width: var(--sidebar-width-collapsed);
  }

  .sidebar__brand-text,
  .sidebar__link-text,
  .sidebar__nav-title,
  .sidebar__user-info {
    opacity: 0;
    width: 0;
    overflow: hidden;
  }

  .main-content {
    margin-left: var(--sidebar-width-collapsed);
  }
}

/* Mobile styles */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar--mobile-open {
    transform: translateX(0);
  }

  .sidebar__mobile-toggle {
    display: block;
  }

  .main-content {
    margin-left: 0;
    padding: 1rem;
  }

  .main-content--mobile-sidebar-open {
    margin-left: 0;
  }

  /* Overlay for mobile sidebar */
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: var(--sidebar-transition);
  }

  .sidebar-overlay--visible {
    opacity: 1;
    visibility: visible;
  }
}

/* Extra small mobile styles */
@media (max-width: 480px) {
  .sidebar__mobile-toggle {
    top: 0.5rem;
    left: 0.5rem;
    padding: 0.5rem;
  }

  .main-content {
    padding: 0.75rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .sidebar,
  .sidebar__link,
  .sidebar__brand-text,
  .sidebar__link-text,
  .sidebar__mobile-toggle,
  .main-content {
    transition: none;
  }

  .sidebar__link:hover {
    transform: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .sidebar {
    border-right-width: 2px;
  }

  .sidebar__link {
    border: 1px solid transparent;
  }

  .sidebar__link:hover,
  .sidebar__link--active {
    border-color: var(--sidebar-primary-blue);
  }
}

/* Dark mode support (for future implementation) */
@media (prefers-color-scheme: dark) {
  :root {
    --sidebar-primary-bg: #0f172a;
    --sidebar-secondary-bg: #1e293b;
    --sidebar-text-primary: #f1f5f9;
    --sidebar-text-secondary: #94a3b8;
    --sidebar-border: #334155;
    --sidebar-light-blue: #1e3a8a;
  }
}

/* Print styles */
@media print {
  .sidebar {
    display: none;
  }

  .main-content {
    margin-left: 0;
  }

  .sidebar__mobile-toggle {
    display: none;
  }
}
